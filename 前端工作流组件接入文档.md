# 前端工作流组件接入文档

## 概述

本文档详细说明了如何在Vue.js项目中接入和使用AuditProcess工作流组件，该组件提供了完整的审批流程管理功能，包括审批记录查看、流程图展示、审批处理等。

## 组件特性

- 🎯 **智能悬浮按钮**：固定在页面右侧，支持收缩/展开状态
- 📋 **抽屉式详情**：包含审批记录和流程图两个标签页
- ✍️ **审批处理**：支持自定义表单的审批弹窗
- 📱 **响应式设计**：完美适配移动端和桌面端
- 🔄 **状态映射**：灵活的业务状态到审批状态映射
- 🎨 **美观UI**：现代化的设计风格，符合星缘Web创作美学纲领

## 快速开始

### 1. 引入组件

```javascript
import AuditProcess from '@/components/Audit/AuditProcess'

export default {
  components: {
    AuditProcess
  }
}
```

### 2. 基础使用

```vue
<template>
  <div class="app-container">
    <!-- 业务内容 -->
    <div class="business-content">
      <!-- 您的业务表单或详情内容 -->
    </div>
    
    <!-- 工作流组件 -->
    <AuditProcess
      v-if="formData.processInstanceId"
      :business-id="formData.id"
      :process-instance-id="formData.processInstanceId"
      :business-status="formData.status"
      :status-mapping="auditStatusMapping"
      :on-before-audit="auditHandle"
      @audit-success="dataUpdateCallback"
    />
  </div>
</template>
```

### 3. 数据准备

```javascript
import { STANDARD_AUDIT_STATUS_MAPPING } from '@/constants/auditStatus'

export default {
  data() {
    return {
      formData: {
        id: null,                    // 业务数据ID
        processInstanceId: null,     // 流程实例ID（关键字段）
        status: null,                // 业务状态
        // ... 其他业务数据
      },
      // 审批状态映射 - 使用公用常量（推荐）
      auditStatusMapping: STANDARD_AUDIT_STATUS_MAPPING
    }
  }
}
```

## 详细配置

### Props 参数说明

| 参数名 | 类型 | 必需 | 默认值 | 说明 |
|--------|------|------|--------|------|
| `business-id` | String/Number | ✅ | - | 业务数据ID |
| `process-instance-id` | String | ✅ | '' | 流程实例ID，为空时不显示组件 |
| `business-status` | Number/String | ❌ | null | 业务状态值，用于状态映射 |
| `status-mapping` | Object | ❌ | {} | 状态映射配置 |
| `final-status` | String | ❌ | '' | 最终状态（优先级最高） |
| `show-return-option` | Boolean | ❌ | false | 是否显示退回选项 |
| `allow-attachment` | Boolean | ❌ | true | 是否允许上传附件 |
| `on-before-audit` | Function | ❌ | null | 审批前置方法 |

### 状态映射配置

#### 推荐方式：使用公用常量

```javascript
import { STANDARD_AUDIT_STATUS_MAPPING } from '@/constants/auditStatus'

export default {
  data() {
    return {
      // 推荐：使用标准审批状态映射常量
      auditStatusMapping: STANDARD_AUDIT_STATUS_MAPPING
    }
  }
}
```

#### 标准状态映射内容

```javascript
// STANDARD_AUDIT_STATUS_MAPPING 的内容
{
  0: 'pending',    // 保存 -> 待提交
  1: 'processing', // 待审核 -> 审批中
  2: 'approved',   // 通过 -> 已通过
  3: 'rejected',   // 不通过 -> 已驳回
  4: 'withdrawn'   // 作废 -> 已撤回
}
```

#### 自定义状态映射（特殊情况）

```javascript
// 如果业务有特殊需求，可以自定义映射
customStatusMapping: {
  'DRAFT': 'pending',      // 草稿 -> 待提交
  'SUBMITTED': 'processing', // 已提交 -> 审批中
  'APPROVED': 'approved',   // 已批准 -> 已通过
  'REJECTED': 'rejected',   // 已拒绝 -> 已驳回
  'CANCELLED': 'withdrawn'  // 已取消 -> 已撤回
}
```

### 事件回调

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `audit-success` | - | 审批处理成功时触发 |
| `refresh` | - | 刷新数据时触发 |

## 完整示例

### 详情页面集成

```vue
<template>
  <div class="app-container oprate-form">
    <el-tabs type="border-card">
      <el-tab-pane label="业务详情">
        <!-- 业务表单内容 -->
        <el-form ref="form" :model="formData" label-width="120px">
          <!-- 表单字段 -->
        </el-form>

        <!-- 操作按钮 -->
        <div class="dialog-footer">
          <el-button v-if="canEdit" type="primary" @click="saveHandle">
            保 存
          </el-button>
          <el-button @click="$returnBack()">返 回</el-button>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 工作流组件 -->
    <AuditProcess
      v-if="formData.processInstanceId"
      :business-id="formData.id"
      :process-instance-id="formData.processInstanceId"
      :business-status="formData.status"
      :status-mapping="auditStatusMapping"
      :on-before-audit="auditHandle"
      @audit-success="dataUpdateCallback"
    />
  </div>
</template>

<script>
import AuditProcess from '@/components/Audit/AuditProcess'
import detailMixin from '@/mixins/detailMixin'
import { STANDARD_AUDIT_STATUS_MAPPING } from '@/constants/auditStatus'

export default {
  name: 'BusinessDetail',
  components: { AuditProcess },
  mixins: [detailMixin],

  data() {
    return {
      formData: {
        id: null,
        processInstanceId: null,
        status: null,
        // ... 其他业务字段
      },

      // API配置
      apis: {
        add: this.businessApi.add,
        update: this.businessApi.update,
        getDetail: this.businessApi.getDetail
      },

      // 审批状态映射 - 使用公用常量
      auditStatusMapping: STANDARD_AUDIT_STATUS_MAPPING
    }
  },
  
  computed: {
    canEdit() {
      return this.isAdd || [0, 3].indexOf(this.formData.status) > -1
    }
  },
  
  mounted() {
    this.pullData()
  },
  
  methods: {
    // 审批前置处理
    async auditHandle() {
      try {
        // 表单验证
        const isValid = await this.validate()
        if (!isValid) {
          return false
        }
        
        // 保存数据
        await this.apis.update(this.formData)
        this.$message.success('保存成功')
        return true
      } catch (error) {
        this.$message.error('保存失败: ' + error.message)
        return false
      }
    },
    
    // 数据更新回调
    dataUpdateCallback() {
      this.freshListPage()
      this.pullData()
    },
    
    // 表单验证
    async validate() {
      try {
        await this.$refs.form.validate()
        return true
      } catch (error) {
        return false
      }
    }
  }
}
</script>
```

### 列表页面集成

```vue
<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <div class="filter-container">
      <el-form :inline="true" :model="queryParams">
        <!-- 搜索字段 -->
      </el-form>
    </div>
    
    <!-- 数据表格 -->
    <el-table :data="tableData" @row-click="handleRowClick">
      <!-- 表格列 -->
    </el-table>
    
    <!-- 分页 -->
    <pagination />
  </div>
</template>

<script>
export default {
  methods: {
    // 行点击跳转到详情页
    handleRowClick(row) {
      this.$router.push({
        path: '/business/detail',
        query: { id: row.id }
      })
    }
  }
}
</script>
```

## 最佳实践

### 1. 数据准备

```javascript
// ✅ 正确：确保流程实例ID存在
v-if="formData.processInstanceId"

// ❌ 错误：缺少流程实例ID
<AuditProcess :business-id="formData.id" />
```

### 2. 状态映射

```javascript
// ✅ 推荐：使用公用常量
import { STANDARD_AUDIT_STATUS_MAPPING } from '@/constants/auditStatus'

auditStatusMapping: STANDARD_AUDIT_STATUS_MAPPING

// ✅ 正确：完整的自定义状态映射
auditStatusMapping: {
  0: 'pending',
  1: 'processing',
  2: 'approved',
  3: 'rejected',
  4: 'withdrawn'
}

// ❌ 错误：状态映射不完整
auditStatusMapping: {
  1: 'processing',
  2: 'approved'
}
```

### 3. 错误处理

```javascript
// ✅ 正确：完善的错误处理
async auditHandle() {
  try {
    const isValid = await this.validate()
    if (!isValid) return false
    
    await this.apis.update(this.formData)
    this.$message.success('保存成功')
    return true
  } catch (error) {
    this.$message.error('保存失败: ' + error.message)
    return false
  }
}

// ❌ 错误：缺少错误处理
async auditHandle() {
  await this.apis.update(this.formData)
  return true
}
```

### 4. 权限控制

```javascript
// ✅ 正确：基于权限控制显示
<AuditProcess
  v-if="formData.processInstanceId && hasAuditPermission"
  :business-id="formData.id"
  :process-instance-id="formData.processInstanceId"
/>
```

## 常见问题

### Q1: 组件不显示怎么办？

**A:** 检查以下几点：
1. 确保 `processInstanceId` 不为空
2. 检查 `v-if` 条件是否正确
3. 确认组件已正确引入和注册

### Q2: 审批状态显示不正确？

**A:** 检查状态映射配置：
1. 确认 `business-status` 值是否正确
2. 检查 `status-mapping` 配置是否完整
3. 验证状态值类型是否匹配

### Q3: 审批前置方法不执行？

**A:** 检查方法配置：
1. 确认 `on-before-audit` 方法存在且可执行
2. 检查方法返回值是否正确
3. 验证异步方法是否正确处理

### Q4: 流程图不显示？

**A:** 检查流程图配置：
1. 确认后端流程图API是否正常
2. 检查流程实例ID是否有效
3. 验证网络请求是否成功

## 样式定制

### 自定义主题色

```scss
// 在全局样式中覆盖默认主题色
.audit-process-container {
  .audit-trigger-btn {
    .trigger-main {
      .status-section .status-badge {
        &.status-processing {
          background: linear-gradient(135deg, #your-color, #your-color-light);
        }
      }
    }
  }
}
```

### 响应式适配

```scss
// 移动端适配
@media (max-width: 768px) {
  .audit-process-container {
    .audit-trigger-btn {
      right: 10px;
      transform: scale(0.9);
    }
  }
}
```

## 性能优化

### 1. 懒加载

```javascript
// 使用动态导入
const AuditProcess = () => import('@/components/Audit/AuditProcess')
```

### 2. 条件渲染

```vue
<!-- 只在需要时渲染组件 -->
<AuditProcess
  v-if="formData.processInstanceId && showAuditComponent"
  :business-id="formData.id"
  :process-instance-id="formData.processInstanceId"
/>
```

### 3. 数据缓存

```javascript
// 缓存审批信息
computed: {
  cachedApprovalInfo() {
    return this.$store.state.approval.cache[this.processInstanceId]
  }
}
```

## 调试指南

### 1. 开启调试模式

在开发环境中，可以通过以下方式开启调试模式：
- 五指捏合手势
- 特殊摇晃操作
- 开发者工具中的调试开关

### 2. 日志输出

```javascript
// 在组件中添加调试日志
watch: {
  'formData.processInstanceId': {
    handler(newVal) {
      console.log('流程实例ID变化:', newVal)
    }
  }
}
```

### 3. 网络请求调试

```javascript
// 在API调用中添加调试信息
async loadApprovalInfo() {
  console.log('开始加载审批信息:', this.processInstanceId)
  try {
    const response = await getApprovalInfo(this.processInstanceId)
    console.log('审批信息加载成功:', response.data)
  } catch (error) {
    console.error('审批信息加载失败:', error)
  }
}
```

## 更新日志

### v1.0.0 (2024-01-01)
- 🎉 初始版本发布
- ✨ 支持基础审批流程管理
- 📱 响应式设计支持

### v1.1.0 (2024-02-01)
- ✨ 新增智能悬浮按钮
- 🎨 优化UI设计风格
- 🔧 修复已知问题

### v1.2.0 (2024-03-01)
- ✨ 支持自定义表单
- 📊 新增流程图展示
- 🚀 性能优化

---

## 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的常见问题部分
2. 检查浏览器控制台的错误信息
3. 联系技术支持团队

**文档版本：** v1.2.0  
**最后更新：** 2024年3月1日  
**维护团队：** 前端开发组
